package com.cdkit.modules.cm.infrastructure.procurement.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.procurement.entity.ProcurementApplicationEntity;
import com.cdkit.modules.cm.domain.procurement.entity.ProcurementApplicationDetailEntity;
import com.cdkit.modules.cm.domain.procurement.repository.ProcurementApplicationRepository;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementApplication;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementApplicationDetail;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementExecutionBasis;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementExecutionBasisDetail;
import com.cdkit.modules.cm.infrastructure.procurement.service.ICostProcurementApplicationService;
import com.cdkit.modules.cm.infrastructure.procurement.service.ICostProcurementApplicationDetailService;
import com.cdkit.modules.cm.infrastructure.procurement.service.ICostProcurementExecutionBasisService;
import com.cdkit.modules.cm.infrastructure.procurement.service.ICostProcurementExecutionBasisDetailService;
import com.cdkit.modules.cm.infrastructure.procurement.converter.ProcurementApplicationConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import cn.hutool.core.util.StrUtil;
import java.util.stream.Collectors;

/**
 * 采购申请仓储实现
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ProcurementApplicationRepositoryImpl implements ProcurementApplicationRepository {

    private final ICostProcurementApplicationService costProcurementApplicationService;
    private final ICostProcurementApplicationDetailService costProcurementApplicationDetailService;
    private final ICostProcurementExecutionBasisService costProcurementExecutionBasisService;
    private final ICostProcurementExecutionBasisDetailService costProcurementExecutionBasisDetailService;

    @Override
    public PageRes<ProcurementApplicationEntity> queryPageList(ProcurementApplicationEntity queryEntity, PageReq pageReq) {
        // 转换为基础设施实体
        CostProcurementApplication queryInfra = ProcurementApplicationConverter.toInfrastructure(queryEntity);

        // 构建查询条件
        LambdaQueryWrapper<CostProcurementApplication> queryWrapper = new LambdaQueryWrapper<>();

        // 添加模糊查询条件
        queryWrapper.like(StrUtil.isNotEmpty(queryInfra.getMaterialName()),
                         CostProcurementApplication::getMaterialName, queryInfra.getMaterialName());
        queryWrapper.like(StrUtil.isNotEmpty(queryInfra.getMaterialCode()),
                         CostProcurementApplication::getMaterialCode, queryInfra.getMaterialCode());
        queryWrapper.like(StrUtil.isNotEmpty(queryInfra.getApplicationNo()),
                         CostProcurementApplication::getApplicationNo, queryInfra.getApplicationNo());

        // 添加精确查询条件
        queryWrapper.eq(StrUtil.isNotEmpty(queryInfra.getQuarter()),
                       CostProcurementApplication::getQuarter, queryInfra.getQuarter());
        queryWrapper.eq(StrUtil.isNotEmpty(queryInfra.getUnit()),
                       CostProcurementApplication::getUnit, queryInfra.getUnit());
        queryWrapper.eq(StrUtil.isNotEmpty(queryInfra.getCreateBy()),
                       CostProcurementApplication::getCreateBy, queryInfra.getCreateBy());

        // 添加时间范围查询
        if (queryInfra.getCreateTime() != null) {
            // 如果有创建时间，查询当天的数据
            String dateStr = new java.text.SimpleDateFormat("yyyy-MM-dd").format(queryInfra.getCreateTime());
            queryWrapper.apply("DATE_FORMAT(create_time, '%Y-%m-%d') = {0}", dateStr);
        }

        // 添加删除标识过滤
        queryWrapper.eq(CostProcurementApplication::getDelFlag, 0);

        // 构建分页对象
        Page<CostProcurementApplication> page = new Page<>(pageReq.getCurrent(), pageReq.getSize());


        // 默认按创建时间倒序
        queryWrapper.orderByDesc(CostProcurementApplication::getCreateTime);


        // 执行分页查询
        Page<CostProcurementApplication> pageList = costProcurementApplicationService.page(page, queryWrapper);
        List<CostProcurementApplication> records = pageList.getRecords();

        // 转换为领域实体（不加载明细，提高查询性能）
        List<ProcurementApplicationEntity> domainEntities = records.stream()
                .map(ProcurementApplicationConverter::toDomain)
                .collect(Collectors.toList());

        return PageRes.of(pageReq.getCurrent(), pageReq.getSize(), domainEntities,
                         pageList.getTotal(), pageList.getPages());
    }

    /**
     * 根据字段名获取对应的数据库列
     */
    private String getColumnByField(String field) {
        switch (field) {
            case "create_time":
                return "create_time";
            case "update_time":
                return "update_time";
            case "application_no":
                return "application_no";
            case "material_code":
                return "material_code";
            case "material_name":
                return "material_name";
            case "current_purchase_quantity":
                return "current_purchase_quantity";
            case "total_price_excluding_tax":
                return "total_price_excluding_tax";
            default:
                return "create_time";
        }
    }

    @Override
    public ProcurementApplicationEntity save(ProcurementApplicationEntity entity) {
        // 转换为基础设施实体
        CostProcurementApplication infraEntity = ProcurementApplicationConverter.toInfrastructure(entity);
        
        // 如果没有申请单号，自动生成
        if (infraEntity.getApplicationNo() == null || infraEntity.getApplicationNo().trim().isEmpty()) {
            infraEntity.setApplicationNo(generateApplicationNo());
        }
        
        // 保存主表
        costProcurementApplicationService.save(infraEntity);
        
        // 保存明细
        if (entity.getDetails() != null && !entity.getDetails().isEmpty()) {
            List<CostProcurementApplicationDetail> detailList = entity.getDetails().stream()
                    .map(detail -> {
                        CostProcurementApplicationDetail infraDetail = ProcurementApplicationConverter.toInfrastructureDetail(detail);
                        infraDetail.setId(null);
                        infraDetail.setApplicationId(infraEntity.getId());
                        return infraDetail;
                    })
                    .collect(Collectors.toList());
            
            costProcurementApplicationDetailService.saveBatch(detailList);
        }
        
        // 返回保存后的实体
        return getById(infraEntity.getId());
    }

    @Override
    public ProcurementApplicationEntity update(ProcurementApplicationEntity entity) {
        // 转换为基础设施实体
        CostProcurementApplication infraEntity = ProcurementApplicationConverter.toInfrastructure(entity);
        
        // 更新主表
        costProcurementApplicationService.updateById(infraEntity);
        
        // 删除原有明细
        LambdaQueryWrapper<CostProcurementApplicationDetail> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(CostProcurementApplicationDetail::getApplicationId, entity.getId());
        costProcurementApplicationDetailService.remove(deleteWrapper);
        
        // 保存新明细
        if (entity.getDetails() != null && !entity.getDetails().isEmpty()) {
            List<CostProcurementApplicationDetail> detailList = entity.getDetails().stream()
                    .map(detail -> {
                        CostProcurementApplicationDetail infraDetail = ProcurementApplicationConverter.toInfrastructureDetail(detail);
                        infraDetail.setApplicationId(entity.getId());
                        infraDetail.setId(null); // 重新生成ID
                        return infraDetail;
                    })
                    .collect(Collectors.toList());
            
            costProcurementApplicationDetailService.saveBatch(detailList);
        }
        
        // 返回更新后的实体
        return getById(entity.getId());
    }

    @Override
    public ProcurementApplicationEntity getById(String id) {
        // 查询主表
        CostProcurementApplication infraEntity = costProcurementApplicationService.getById(id);
        if (infraEntity == null) {
            return null;
        }
        
        // 查询明细
        LambdaQueryWrapper<CostProcurementApplicationDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(CostProcurementApplicationDetail::getApplicationId, id)
                .eq(CostProcurementApplicationDetail::getDelFlag, 0)
                .orderByAsc(CostProcurementApplicationDetail::getCreateTime);
        
        List<CostProcurementApplicationDetail> detailList = costProcurementApplicationDetailService.list(detailWrapper);
        
        // 转换为领域实体
        ProcurementApplicationEntity domainEntity = ProcurementApplicationConverter.toDomain(infraEntity);
        if (detailList != null && !detailList.isEmpty()) {
            List<ProcurementApplicationDetailEntity> domainDetailList = detailList.stream()
                    .map(ProcurementApplicationConverter::toDomainDetail)
                    .collect(Collectors.toList());
            domainEntity.setDetails(domainDetailList);
        }
        
        return domainEntity;
    }

    @Override
    public void deleteById(String id) {
        // 删除明细（逻辑删除）
        LambdaQueryWrapper<CostProcurementApplicationDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(CostProcurementApplicationDetail::getApplicationId, id);
        costProcurementApplicationDetailService.remove(detailWrapper);
        
        // 删除主表（逻辑删除）
        costProcurementApplicationService.removeById(id);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        for (String id : ids) {
            deleteById(id);
        }
    }

    @Override
    public String generateApplicationNo() {
        // 格式：PA + YYYYMMDD + 4位序号
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String prefix = "PA" + dateStr;
        
        // 查询当天已有的最大序号
        LambdaQueryWrapper<CostProcurementApplication> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.likeRight(CostProcurementApplication::getApplicationNo, prefix)
                .eq(CostProcurementApplication::getDelFlag, 0)
                .orderByDesc(CostProcurementApplication::getApplicationNo)
                .last("LIMIT 1");
        
        CostProcurementApplication lastApplication = costProcurementApplicationService.getOne(queryWrapper);
        
        int nextSequence = 1;
        if (lastApplication != null && lastApplication.getApplicationNo() != null) {
            String lastNo = lastApplication.getApplicationNo();
            if (lastNo.length() >= 14) { // PA + 8位日期 + 4位序号
                try {
                    String sequenceStr = lastNo.substring(10); // 获取后4位序号
                    int lastSequence = Integer.parseInt(sequenceStr);
                    nextSequence = lastSequence + 1;
                } catch (NumberFormatException e) {
                    log.warn("解析申请单号序号失败: {}", lastNo, e);
                }
            }
        }
        
        return prefix + String.format("%04d", nextSequence);
    }

    @Override
    public ProcurementApplicationEntity findByApplicationNo(String applicationNo) {
        LambdaQueryWrapper<CostProcurementApplication> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostProcurementApplication::getApplicationNo, applicationNo)
                .eq(CostProcurementApplication::getDelFlag, 0);
        
        CostProcurementApplication infraEntity = costProcurementApplicationService.getOne(queryWrapper);
        if (infraEntity == null) {
            return null;
        }
        
        return getById(infraEntity.getId());
    }

    @Override
    public boolean existsByApplicationNo(String applicationNo) {
        LambdaQueryWrapper<CostProcurementApplication> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostProcurementApplication::getApplicationNo, applicationNo)
                .eq(CostProcurementApplication::getDelFlag, 0);
        
        return costProcurementApplicationService.count(queryWrapper) > 0;
    }

    @Override
    public void updateExecutionBasisPurchasedQuantity(ProcurementApplicationEntity entity) {
        if (entity == null || entity.getMaterialCode() == null || entity.getQuarter() == null) {
            log.warn("采购申请实体或关键字段为空，无法更新采购执行依据已采购量");
            return;
        }

        try {
            // 1. 查找对应的采购执行依据主表
            LambdaQueryWrapper<CostProcurementExecutionBasis> mainWrapper = new LambdaQueryWrapper<>();
            mainWrapper.eq(CostProcurementExecutionBasis::getMaterialCode, entity.getMaterialCode())
                    .eq(CostProcurementExecutionBasis::getQuarter, entity.getQuarter())
                    .eq(CostProcurementExecutionBasis::getDelFlag, 0);

            CostProcurementExecutionBasis executionBasis = costProcurementExecutionBasisService.getOne(mainWrapper);
            if (executionBasis == null) {
                log.warn("未找到对应的采购执行依据，物料编码: {}, 季度: {}",
                        entity.getMaterialCode(), entity.getQuarter());
                return;
            }

            // 2. 更新主表已采购量和金额
            updateExecutionBasisMainAmounts(executionBasis, entity);

            log.info("更新采购执行依据主表成功，ID: {}, 本次采购量: {}, 本次金额（不含税）: {}, 本次金额（含税）: {}",
                    executionBasis.getId(), entity.getCurrentPurchaseQuantity(),
                    entity.getTotalPriceExcludingTax(), entity.getTotalPriceIncludingTax());

            // 3. 更新明细表已采购量和金额
            if (entity.getDetails() != null && !entity.getDetails().isEmpty()) {
                for (ProcurementApplicationDetailEntity detail : entity.getDetails()) {
                    updateExecutionBasisDetailAmounts(executionBasis.getId(), detail);
                }
            }

        } catch (Exception e) {
            log.error("更新采购执行依据已采购量失败，物料编码: {}, 季度: {}",
                    entity.getMaterialCode(), entity.getQuarter(), e);
            throw new RuntimeException("更新采购执行依据已采购量失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新采购执行依据主表的已采购量和金额
     */
    private void updateExecutionBasisMainAmounts(CostProcurementExecutionBasis executionBasis,
                                                ProcurementApplicationEntity entity) {
        // 1. 更新已采购量
        BigDecimal currentPurchased = executionBasis.getPurchasedQuantity() != null ?
                executionBasis.getPurchasedQuantity() : BigDecimal.ZERO;
        BigDecimal newPurchased = currentPurchased.add(entity.getCurrentPurchaseQuantity());
        executionBasis.setPurchasedQuantity(newPurchased);

        // 2. 更新已支出金额
        BigDecimal currentSpentExcludingTax = executionBasis.getSpentAmountExcludingTax() != null ?
                executionBasis.getSpentAmountExcludingTax() : BigDecimal.ZERO;
        BigDecimal currentSpentIncludingTax = executionBasis.getSpentAmountIncludingTax() != null ?
                executionBasis.getSpentAmountIncludingTax() : BigDecimal.ZERO;

        BigDecimal newSpentExcludingTax = currentSpentExcludingTax.add(entity.getTotalPriceExcludingTax());
        BigDecimal newSpentIncludingTax = currentSpentIncludingTax.add(entity.getTotalPriceIncludingTax());

        executionBasis.setSpentAmountExcludingTax(newSpentExcludingTax);
        executionBasis.setSpentAmountIncludingTax(newSpentIncludingTax);

        // 3. 计算剩余金额
        BigDecimal totalAmountExcludingTax = executionBasis.getTotalPriceExcludingTax() != null ?
                executionBasis.getTotalPriceExcludingTax() : BigDecimal.ZERO;
        BigDecimal totalAmountIncludingTax = executionBasis.getTotalPriceIncludingTax() != null ?
                executionBasis.getTotalPriceIncludingTax() : BigDecimal.ZERO;

        BigDecimal remainingAmountExcludingTax = totalAmountExcludingTax.subtract(newSpentExcludingTax);
        BigDecimal remainingAmountIncludingTax = totalAmountIncludingTax.subtract(newSpentIncludingTax);

        // 4. 数据校验：确保剩余金额不为负数
        if (remainingAmountExcludingTax.compareTo(BigDecimal.ZERO) < 0) {
            log.warn("更新后剩余金额（不含税）为负数，执行依据ID: {}, 剩余金额: {}",
                    executionBasis.getId(), remainingAmountExcludingTax);
        }
        if (remainingAmountIncludingTax.compareTo(BigDecimal.ZERO) < 0) {
            log.warn("更新后剩余金额（含税）为负数，执行依据ID: {}, 剩余金额: {}",
                    executionBasis.getId(), remainingAmountIncludingTax);
        }

        executionBasis.setRemainingAmountExcludingTax(remainingAmountExcludingTax);
        executionBasis.setRemainingAmountIncludingTax(remainingAmountIncludingTax);

        // 5. 保存更新
        costProcurementExecutionBasisService.updateById(executionBasis);

        log.info("更新采购执行依据主表金额成功，ID: {}, 已采购量: {} -> {}, 已支出金额（不含税）: {} -> {}, 已支出金额（含税）: {} -> {}, 剩余金额（不含税）: {}, 剩余金额（含税）: {}",
                executionBasis.getId(), currentPurchased, newPurchased,
                currentSpentExcludingTax, newSpentExcludingTax,
                currentSpentIncludingTax, newSpentIncludingTax,
                remainingAmountExcludingTax, remainingAmountIncludingTax);
    }

    /**
     * 更新采购执行依据明细表的已采购量和金额
     */
    private void updateExecutionBasisDetailAmounts(String executionBasisId,
                                                  ProcurementApplicationDetailEntity detail) {
        if (detail == null || detail.getBudgetCode() == null) {
            log.warn("采购申请明细或预算编码为空，跳过更新");
            return;
        }

        try {
            // 查找对应的采购执行依据明细
            LambdaQueryWrapper<CostProcurementExecutionBasisDetail> detailWrapper = new LambdaQueryWrapper<>();
            detailWrapper.eq(CostProcurementExecutionBasisDetail::getExecutionBasisId, executionBasisId)
                    .eq(CostProcurementExecutionBasisDetail::getQuarterlyBudgetNo, detail.getBudgetCode())
                    .eq(CostProcurementExecutionBasisDetail::getDelFlag, 0);

            CostProcurementExecutionBasisDetail executionDetail = costProcurementExecutionBasisDetailService.getOne(detailWrapper);
            if (executionDetail == null) {
                log.warn("未找到对应的采购执行依据明细，执行依据ID: {}, 预算编码: {}",
                        executionBasisId, detail.getBudgetCode());
                return;
            }

            // 更新明细已采购量
            BigDecimal currentDetailPurchased = executionDetail.getPurchasedQuantity() != null ?
                    executionDetail.getPurchasedQuantity() : BigDecimal.ZERO;
            BigDecimal newDetailPurchased = currentDetailPurchased.add(detail.getCurrentPurchaseQuantity());
            executionDetail.setPurchasedQuantity(newDetailPurchased);
            costProcurementExecutionBasisDetailService.updateById(executionDetail);

            log.info("更新采购执行依据明细已采购量成功，ID: {}, 预算编码: {}, 原已采购量: {}, 本次采购量: {}, 新已采购量: {}",
                    executionDetail.getId(), detail.getBudgetCode(), currentDetailPurchased,
                    detail.getCurrentPurchaseQuantity(), newDetailPurchased);

        } catch (Exception e) {
            log.error("更新采购执行依据明细已采购量失败，执行依据ID: {}, 预算编码: {}",
                    executionBasisId, detail.getBudgetCode(), e);
            throw new RuntimeException("更新采购执行依据明细已采购量失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void updateExecutionBasisPurchasedQuantityByDelta(ProcurementApplicationEntity originalEntity,
                                                             ProcurementApplicationEntity newEntity) {
        if (originalEntity == null || newEntity == null ||
            newEntity.getMaterialCode() == null || newEntity.getQuarter() == null) {
            log.warn("采购申请实体或关键字段为空，无法进行差量更新");
            return;
        }

        try {
            // 1. 计算主表采购量差值
            BigDecimal originalQuantity = originalEntity.getCurrentPurchaseQuantity() != null ?
                    originalEntity.getCurrentPurchaseQuantity() : BigDecimal.ZERO;
            BigDecimal newQuantity = newEntity.getCurrentPurchaseQuantity() != null ?
                    newEntity.getCurrentPurchaseQuantity() : BigDecimal.ZERO;
            BigDecimal quantityDelta = newQuantity.subtract(originalQuantity);

            log.info("开始差量更新采购执行依据，物料编码: {}, 季度: {}, 原采购量: {}, 新采购量: {}, 差量: {}",
                    newEntity.getMaterialCode(), newEntity.getQuarter(), originalQuantity, newQuantity, quantityDelta);

            // 如果差量为0，无需更新
            if (quantityDelta.compareTo(BigDecimal.ZERO) == 0) {
                log.info("采购量无变化，跳过差量更新");
                return;
            }

            // 2. 查找对应的采购执行依据主表
            LambdaQueryWrapper<CostProcurementExecutionBasis> mainWrapper = new LambdaQueryWrapper<>();
            mainWrapper.eq(CostProcurementExecutionBasis::getMaterialCode, newEntity.getMaterialCode())
                    .eq(CostProcurementExecutionBasis::getQuarter, newEntity.getQuarter())
                    .eq(CostProcurementExecutionBasis::getDelFlag, 0);

            CostProcurementExecutionBasis executionBasis = costProcurementExecutionBasisService.getOne(mainWrapper);
            if (executionBasis == null) {
                log.warn("未找到对应的采购执行依据，物料编码: {}, 季度: {}",
                        newEntity.getMaterialCode(), newEntity.getQuarter());
                return;
            }

            // 3. 更新主表数据
            updateExecutionBasisMainByDelta(executionBasis, quantityDelta, newEntity.getUnitPriceExcludingTax());

            // 4. 更新明细表数据
            if (newEntity.getDetails() != null && !newEntity.getDetails().isEmpty()) {
                updateExecutionBasisDetailsByDelta(executionBasis.getId(), originalEntity.getDetails(), newEntity.getDetails());
            }

        } catch (Exception e) {
            log.error("差量更新采购执行依据失败，物料编码: {}, 季度: {}",
                    newEntity.getMaterialCode(), newEntity.getQuarter(), e);
            throw new RuntimeException("差量更新采购执行依据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 差量更新采购执行依据主表
     */
    private void updateExecutionBasisMainByDelta(CostProcurementExecutionBasis executionBasis,
                                                BigDecimal quantityDelta,
                                                BigDecimal unitPriceExcludingTax) {
        // 计算原值
        BigDecimal originalPurchasedQuantity = executionBasis.getPurchasedQuantity() != null ?
                executionBasis.getPurchasedQuantity() : BigDecimal.ZERO;
        BigDecimal originalSpentAmountExcludingTax = executionBasis.getSpentAmountExcludingTax() != null ?
                executionBasis.getSpentAmountExcludingTax() : BigDecimal.ZERO;
        BigDecimal originalSpentAmountIncludingTax = executionBasis.getSpentAmountIncludingTax() != null ?
                executionBasis.getSpentAmountIncludingTax() : BigDecimal.ZERO;

        // 计算新值
        BigDecimal newPurchasedQuantity = originalPurchasedQuantity.add(quantityDelta);

        // 数据校验：确保已采购量不为负数
        if (newPurchasedQuantity.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("更新后的已采购量不能为负数，当前值: " + originalPurchasedQuantity +
                    ", 差量: " + quantityDelta);
        }

        // 计算金额差值
        BigDecimal amountDeltaExcludingTax = BigDecimal.ZERO;
        BigDecimal amountDeltaIncludingTax = BigDecimal.ZERO;

        if (unitPriceExcludingTax != null && quantityDelta.compareTo(BigDecimal.ZERO) != 0) {
            amountDeltaExcludingTax = quantityDelta.multiply(unitPriceExcludingTax);
            amountDeltaIncludingTax = amountDeltaExcludingTax.multiply(new BigDecimal("1.13")); // 假设税率13%
        }

        BigDecimal newSpentAmountExcludingTax = originalSpentAmountExcludingTax.add(amountDeltaExcludingTax);
        BigDecimal newSpentAmountIncludingTax = originalSpentAmountIncludingTax.add(amountDeltaIncludingTax);

        // 数据校验：确保已支出金额不为负数
        if (newSpentAmountExcludingTax.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("更新后的已支出金额（不含税）不能为负数");
        }
        if (newSpentAmountIncludingTax.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("更新后的已支出金额（含税）不能为负数");
        }

        // 更新数据
        executionBasis.setPurchasedQuantity(newPurchasedQuantity);
        executionBasis.setSpentAmountExcludingTax(newSpentAmountExcludingTax);
        executionBasis.setSpentAmountIncludingTax(newSpentAmountIncludingTax);

        // 计算剩余金额
        BigDecimal totalAmountExcludingTax = executionBasis.getTotalPriceExcludingTax() != null ?
                executionBasis.getTotalPriceExcludingTax() : BigDecimal.ZERO;
        BigDecimal totalAmountIncludingTax = executionBasis.getTotalPriceIncludingTax() != null ?
                executionBasis.getTotalPriceIncludingTax() : BigDecimal.ZERO;

        executionBasis.setRemainingAmountExcludingTax(totalAmountExcludingTax.subtract(newSpentAmountExcludingTax));
        executionBasis.setRemainingAmountIncludingTax(totalAmountIncludingTax.subtract(newSpentAmountIncludingTax));

        costProcurementExecutionBasisService.updateById(executionBasis);

        log.info("差量更新采购执行依据主表成功，ID: {}, 已采购量: {} -> {}, 已支出金额（不含税）: {} -> {}, 已支出金额（含税）: {} -> {}",
                executionBasis.getId(), originalPurchasedQuantity, newPurchasedQuantity,
                originalSpentAmountExcludingTax, newSpentAmountExcludingTax,
                originalSpentAmountIncludingTax, newSpentAmountIncludingTax);
    }

    /**
     * 差量更新采购执行依据明细表
     */
    private void updateExecutionBasisDetailsByDelta(String executionBasisId,
                                                   List<ProcurementApplicationDetailEntity> originalDetails,
                                                   List<ProcurementApplicationDetailEntity> newDetails) {
        if (newDetails == null || newDetails.isEmpty()) {
            log.warn("新明细列表为空，跳过明细差量更新");
            return;
        }

        // 将原明细转换为Map，便于查找
        Map<String, ProcurementApplicationDetailEntity> originalDetailMap = new HashMap<>();
        if (originalDetails != null) {
            originalDetails.forEach(detail -> {
                if (detail.getBudgetCode() != null) {
                    originalDetailMap.put(detail.getBudgetCode(), detail);
                }
            });
        }

        // 遍历新明细，计算差量并更新
        for (ProcurementApplicationDetailEntity newDetail : newDetails) {
            if (newDetail.getBudgetCode() == null) {
                log.warn("明细预算编码为空，跳过该明细");
                continue;
            }

            // 获取原明细
            ProcurementApplicationDetailEntity originalDetail = originalDetailMap.get(newDetail.getBudgetCode());

            // 计算差量
            BigDecimal originalQuantity = originalDetail != null && originalDetail.getCurrentPurchaseQuantity() != null ?
                    originalDetail.getCurrentPurchaseQuantity() : BigDecimal.ZERO;
            BigDecimal newQuantity = newDetail.getCurrentPurchaseQuantity() != null ?
                    newDetail.getCurrentPurchaseQuantity() : BigDecimal.ZERO;
            BigDecimal quantityDelta = newQuantity.subtract(originalQuantity);

            // 如果差量为0，跳过
            if (quantityDelta.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }

            // 更新明细
            updateExecutionBasisDetailByDelta(executionBasisId, newDetail.getBudgetCode(),
                    quantityDelta, newDetail.getUnitPriceExcludingTax());
        }
    }

    /**
     * 差量更新单个采购执行依据明细
     */
    private void updateExecutionBasisDetailByDelta(String executionBasisId, String budgetCode,
                                                  BigDecimal quantityDelta, BigDecimal unitPriceExcludingTax) {
        try {
            // 查找对应的采购执行依据明细
            LambdaQueryWrapper<CostProcurementExecutionBasisDetail> detailWrapper = new LambdaQueryWrapper<>();
            detailWrapper.eq(CostProcurementExecutionBasisDetail::getExecutionBasisId, executionBasisId)
                    .eq(CostProcurementExecutionBasisDetail::getQuarterlyBudgetNo, budgetCode)
                    .eq(CostProcurementExecutionBasisDetail::getDelFlag, 0);

            CostProcurementExecutionBasisDetail executionDetail = costProcurementExecutionBasisDetailService.getOne(detailWrapper);
            if (executionDetail == null) {
                log.warn("未找到对应的采购执行依据明细，执行依据ID: {}, 预算编码: {}",
                        executionBasisId, budgetCode);
                return;
            }

            // 计算原值
            BigDecimal originalPurchasedQuantity = executionDetail.getPurchasedQuantity() != null ?
                    executionDetail.getPurchasedQuantity() : BigDecimal.ZERO;
            BigDecimal originalSpentAmountExcludingTax = executionDetail.getSpentAmountExcludingTax() != null ?
                    executionDetail.getSpentAmountExcludingTax() : BigDecimal.ZERO;
            BigDecimal originalSpentAmountIncludingTax = executionDetail.getSpentAmountIncludingTax() != null ?
                    executionDetail.getSpentAmountIncludingTax() : BigDecimal.ZERO;

            // 计算新值
            BigDecimal newPurchasedQuantity = originalPurchasedQuantity.add(quantityDelta);

            // 数据校验：确保已采购量不为负数
            if (newPurchasedQuantity.compareTo(BigDecimal.ZERO) < 0) {
                throw new IllegalArgumentException("明细更新后的已采购量不能为负数，预算编码: " + budgetCode +
                        ", 当前值: " + originalPurchasedQuantity + ", 差量: " + quantityDelta);
            }

            // 计算金额差值
            BigDecimal amountDeltaExcludingTax = BigDecimal.ZERO;
            BigDecimal amountDeltaIncludingTax = BigDecimal.ZERO;

            if (unitPriceExcludingTax != null && quantityDelta.compareTo(BigDecimal.ZERO) != 0) {
                amountDeltaExcludingTax = quantityDelta.multiply(unitPriceExcludingTax);
                amountDeltaIncludingTax = amountDeltaExcludingTax.multiply(new BigDecimal("1.13")); // 假设税率13%
            }

            BigDecimal newSpentAmountExcludingTax = originalSpentAmountExcludingTax.add(amountDeltaExcludingTax);
            BigDecimal newSpentAmountIncludingTax = originalSpentAmountIncludingTax.add(amountDeltaIncludingTax);

            // 数据校验：确保已支出金额不为负数
            if (newSpentAmountExcludingTax.compareTo(BigDecimal.ZERO) < 0) {
                throw new IllegalArgumentException("明细更新后的已支出金额（不含税）不能为负数，预算编码: " + budgetCode);
            }
            if (newSpentAmountIncludingTax.compareTo(BigDecimal.ZERO) < 0) {
                throw new IllegalArgumentException("明细更新后的已支出金额（含税）不能为负数，预算编码: " + budgetCode);
            }

            // 更新数据
            executionDetail.setPurchasedQuantity(newPurchasedQuantity);
            executionDetail.setSpentAmountExcludingTax(newSpentAmountExcludingTax);
            executionDetail.setSpentAmountIncludingTax(newSpentAmountIncludingTax);

            // 计算剩余金额
            BigDecimal totalAmountExcludingTax = executionDetail.getTotalPriceExcludingTax() != null ?
                    executionDetail.getTotalPriceExcludingTax() : BigDecimal.ZERO;
            BigDecimal totalAmountIncludingTax = executionDetail.getTotalPriceIncludingTax() != null ?
                    executionDetail.getTotalPriceIncludingTax() : BigDecimal.ZERO;

            executionDetail.setRemainingAmountExcludingTax(totalAmountExcludingTax.subtract(newSpentAmountExcludingTax));
            executionDetail.setRemainingAmountIncludingTax(totalAmountIncludingTax.subtract(newSpentAmountIncludingTax));

            costProcurementExecutionBasisDetailService.updateById(executionDetail);

            log.info("差量更新采购执行依据明细成功，ID: {}, 预算编码: {}, 已采购量: {} -> {}, 已支出金额（不含税）: {} -> {}, 已支出金额（含税）: {} -> {}",
                    executionDetail.getId(), budgetCode, originalPurchasedQuantity, newPurchasedQuantity,
                    originalSpentAmountExcludingTax, newSpentAmountExcludingTax,
                    originalSpentAmountIncludingTax, newSpentAmountIncludingTax);

        } catch (Exception e) {
            log.error("差量更新采购执行依据明细失败，执行依据ID: {}, 预算编码: {}",
                    executionBasisId, budgetCode, e);
            throw new RuntimeException("差量更新采购执行依据明细失败: " + e.getMessage(), e);
        }
    }
}

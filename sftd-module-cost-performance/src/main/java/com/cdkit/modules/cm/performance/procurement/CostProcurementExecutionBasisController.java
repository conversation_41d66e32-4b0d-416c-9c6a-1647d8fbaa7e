package com.cdkit.modules.cm.performance.procurement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.api.vo.Result;
import com.cdkit.modules.cm.api.procurement.dto.CostProcurementExecutionBasisDTO;
import com.cdkit.modules.cm.api.procurement.dto.CostProcurementExecutionBasisQueryDTO;
import com.cdkit.modules.cm.api.procurement.dto.MaterialDTO;
import com.cdkit.modules.cm.application.procurement.ProcurementExecutionBasisApplication;
import com.cdkit.modules.cm.application.procurement.converter.CostProcurementExecutionBasisConverter;
import com.cdkit.modules.cm.domain.procurement.entity.CostProcurementExecutionBasisEntity;
import com.cdkit.common.page.PageRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.util.StringUtils;

// Excel导出相关
import com.cdkitframework.poi.excel.def.NormalExcelConstants;
import com.cdkitframework.poi.excel.entity.ExportParams;
import com.cdkitframework.poi.excel.view.CdkitEntityExcelView;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Date;
import java.util.List;

/**
 * 采购执行依据控制器
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Tag(name = "采购执行依据管理", description = "采购执行依据相关接口")
@RestController
@RequestMapping("/cm/costProcurementExecutionBasis")
@RequiredArgsConstructor
@Slf4j
public class CostProcurementExecutionBasisController {

    private final ProcurementExecutionBasisApplication procurementExecutionBasisApplication;

    /**
     * 分页查询采购执行依据列表
     * 支持材料名称、材料编码模糊查询，开始时间和结束时间查询
     * 按照创建时间倒序排列，使用子母表表现形式
     *
     * @param materialName 材料名称（支持模糊查询）
     * @param materialCode 材料编码（支持模糊查询）
     * @param startDate 开始时间（年月）
     * @param endDate 结束时间（年月）
     * @param quarter 所在季度
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    @Operation(summary = "采购执行依据-分页列表查询", description = "支持材料名称、材料编码模糊查询，开始时间和结束时间查询，按创建时间倒序排列")
    @GetMapping("/list")
    public Result<IPage<CostProcurementExecutionBasisDTO>> queryPageList(
            @Parameter(description = "材料名称") @RequestParam(required = false) String materialName,
            @Parameter(description = "材料编码") @RequestParam(required = false) String materialCode,
            @Parameter(description = "开始时间（年月）") @RequestParam(required = false) String startDate,
            @Parameter(description = "结束时间（年月）") @RequestParam(required = false) String endDate,
            @Parameter(description = "所在季度") @RequestParam(required = false) String quarter,
            @Parameter(description = "页码") @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @Parameter(description = "每页数量") @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {

        try {
            // 构建查询条件
            CostProcurementExecutionBasisQueryDTO queryDTO = new CostProcurementExecutionBasisQueryDTO();
            queryDTO.setMaterialName(materialName);
            queryDTO.setMaterialCode(materialCode);
            queryDTO.setQuarter(quarter);

            // 处理startDate字符串转Date
            if (StringUtils.hasText(startDate)) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
                    Date date = sdf.parse(startDate);
                    queryDTO.setStartDate(date);
                } catch (Exception e) {
                    log.warn("开始时间格式错误: {}", startDate, e);
                }
            }

            // 处理endDate字符串转Date
            if (StringUtils.hasText(endDate)) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
                    Date date = sdf.parse(endDate);
                    queryDTO.setEndDate(date);
                } catch (Exception e) {
                    log.warn("结束时间格式错误: {}", endDate, e);
                }
            }

            // 转换为领域实体
            CostProcurementExecutionBasisEntity queryEntity = CostProcurementExecutionBasisConverter.toEntity(queryDTO);

            // 执行查询
            PageRes<CostProcurementExecutionBasisEntity> pageRes = procurementExecutionBasisApplication.queryPageList(queryEntity, pageNo, pageSize);

            // 构建返回结果
            IPage<CostProcurementExecutionBasisDTO> page = new Page<>(pageNo, pageSize);
            if (pageRes != null) {
                page.setCurrent(pageRes.getCurrent());
                page.setSize(pageRes.getSize());
                page.setTotal(pageRes.getTotal());
                page.setRecords(CostProcurementExecutionBasisConverter.toDTOList(pageRes.getRecords()));
            }

            return Result.OK(page);

        } catch (Exception e) {
            log.error("查询采购执行依据列表失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询采购执行依据详情
     * 
     * @param id 采购执行依据ID
     * @return 采购执行依据详情
     */
    @Operation(summary = "采购执行依据-详情查询", description = "根据ID查询采购执行依据详情，包含明细信息")
    @GetMapping("/{id}")
    public Result<CostProcurementExecutionBasisDTO> getById(@PathVariable String id) {
        try {
            CostProcurementExecutionBasisEntity entity = procurementExecutionBasisApplication.getById(id);
            if (entity == null) {
                return Result.error("采购执行依据不存在");
            }
            
            CostProcurementExecutionBasisDTO dto = CostProcurementExecutionBasisConverter.toDTO(entity);
            return Result.OK(dto);
            
        } catch (Exception e) {
            log.error("查询采购执行依据详情失败，ID: {}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据物料编码查询采购执行依据信息
     * 一个物料编码可能对应多个不同的季度数据
     *
     * @param materialCode 物料编码
     * @return 采购执行依据列表（包含所有季度的数据）
     */
    @Operation(summary = "采购执行依据-根据物料编码查询", description = "根据物料编码查询采购执行依据信息，返回该物料下所有季度的数据")
    @GetMapping("/findByMaterialCode")
    public Result<List<CostProcurementExecutionBasisDTO>> findByMaterialCode(
            @Parameter(description = "物料编码", required = true) @RequestParam String materialCode) {
        try {
            if (materialCode == null || materialCode.trim().isEmpty()) {
                return Result.error("物料编码不能为空");
            }

            List<CostProcurementExecutionBasisEntity> entities = procurementExecutionBasisApplication.findByMaterialCode(materialCode);
            List<CostProcurementExecutionBasisDTO> dtoList = CostProcurementExecutionBasisConverter.toDTOList(entities);

            return Result.OK(dtoList);

        } catch (Exception e) {
            log.error("根据物料编码查询采购执行依据失败，物料编码: {}", materialCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据物料编码和季度查询采购执行依据信息
     *
     * @param materialCode 物料编码
     * @param quarter 所在季度
     * @return 采购执行依据实体
     */
    @Operation(summary = "采购执行依据-根据物料编码和季度查询", description = "根据物料编码和季度查询采购执行依据信息")
    @GetMapping("/findByMaterialCodeAndQuarter")
    public Result<CostProcurementExecutionBasisDTO> findByMaterialCodeAndQuarter(
            @Parameter(description = "物料编码", required = true) @RequestParam String materialCode,
            @Parameter(description = "所在季度", required = true) @RequestParam String quarter) {
        try {
            if (materialCode == null || materialCode.trim().isEmpty()) {
                return Result.error("物料编码不能为空");
            }
            if (quarter == null || quarter.trim().isEmpty()) {
                return Result.error("季度不能为空");
            }

            CostProcurementExecutionBasisEntity entity = procurementExecutionBasisApplication.findByMaterialCodeAndQuarter(materialCode, quarter);
            if (entity == null) {
                return Result.error("未找到对应的采购执行依据数据");
            }

            CostProcurementExecutionBasisDTO dto = CostProcurementExecutionBasisConverter.toDTO(entity);
            return Result.OK(dto);

        } catch (Exception e) {
            log.error("根据物料编码和季度查询采购执行依据失败，物料编码: {}, 季度: {}", materialCode, quarter, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询所有物料编码和物料名称组合（去重）
     * 用于物料选择下拉框等场景
     *
     * @return 物料信息列表（去重后的物料编码、物料名称、计量单位）
     */
    @Operation(summary = "采购执行依据-查询所有物料信息", description = "查询系统中所有的物料编码和物料名称组合，去重后返回，用于下拉框选择")
    @GetMapping("/getMaterialList")
    public Result<List<MaterialDTO>> getMaterialList() {
        try {
            List<CostProcurementExecutionBasisEntity> entities = procurementExecutionBasisApplication.findAllMaterials();

            // 转换为MaterialDTO
            List<MaterialDTO> materialList = entities.stream()
                    .map(entity -> MaterialDTO.builder()
                            .materialCode(entity.getMaterialCode())
                            .materialName(entity.getMaterialName())
                            .unit(entity.getUnit())
                            .build())
                    .collect(Collectors.toList());

            return Result.OK(materialList);

        } catch (Exception e) {
            log.error("查询物料信息列表失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 导出采购执行依据数据到Excel
     *
     * @param materialName 材料名称（支持模糊查询）
     * @param materialCode 材料编码（支持模糊查询）
     * @param startDate 开始时间（年月）
     * @param endDate 结束时间（年月）
     * @param quarter 所在季度
     * @return Excel文件
     */
    @Operation(summary = "采购执行依据-导出Excel", description = "导出符合条件的采购执行依据数据到Excel文件")
    @GetMapping("/exportXls")
    public ModelAndView exportXls(
            @Parameter(description = "材料名称") @RequestParam(required = false) String materialName,
            @Parameter(description = "材料编码") @RequestParam(required = false) String materialCode,
            @Parameter(description = "开始时间（年月）") @RequestParam(required = false) String startDate,
            @Parameter(description = "结束时间（年月）") @RequestParam(required = false) String endDate,
            @Parameter(description = "所在季度") @RequestParam(required = false) String quarter) {

        try {
            log.info("开始导出采购执行依据Excel数据，材料名称: {}, 材料编码: {}", materialName, materialCode);

            // Step.1 获取当前用户信息
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String userName = "系统用户";
            if (sysUser != null) {
                userName = sysUser.getRealname();
            }

            // Step.2 构建查询条件
            CostProcurementExecutionBasisQueryDTO queryDTO = new CostProcurementExecutionBasisQueryDTO();
            queryDTO.setMaterialName(materialName);
            queryDTO.setMaterialCode(materialCode);
            queryDTO.setQuarter(quarter);

            // 处理executionDate字符串转Date
            if (StringUtils.hasText(executionDate)) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Date date = sdf.parse(executionDate);
                    queryDTO.setExecutionDate(date);
                } catch (Exception e) {
                    log.warn("执行日期格式错误: {}", executionDate, e);
                }
            }

            // Step.3 转换为领域实体
            CostProcurementExecutionBasisEntity queryEntity = CostProcurementExecutionBasisConverter.toEntity(queryDTO);

            // Step.4 通过应用层获取导出数据（包含明细数据）
            List<CostProcurementExecutionBasisEntity> entityList = procurementExecutionBasisApplication.getExportList(queryEntity);

            // Step.5 转换为DTO列表（包含明细信息）
            List<CostProcurementExecutionBasisDTO> exportList = CostProcurementExecutionBasisConverter.toDTOList(entityList);

            // 确保导出列表不为null
            if (exportList == null) {
                exportList = Arrays.asList();
            }

            // Step.6 使用Cdkit包的导出逻辑 - 使用CostProcurementExecutionBasisDTO导出主子表
            ModelAndView mv = new ModelAndView(new CdkitEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "采购执行依据列表");
            mv.addObject(NormalExcelConstants.CLASS, CostProcurementExecutionBasisDTO.class);
            mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("采购执行依据数据", "导出人:" + userName, "采购执行依据"));
            mv.addObject(NormalExcelConstants.DATA_LIST, exportList);

            log.info("导出采购执行依据Excel成功，共{}条数据", exportList.size());
            return mv;

        } catch (Exception e) {
            log.error("导出采购执行依据Excel失败", e);
            // 即使出现异常，也尝试导出空Excel
            try {
                LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                String userName = sysUser != null ? sysUser.getRealname() : "系统用户";

                ModelAndView mv = new ModelAndView(new CdkitEntityExcelView());
                mv.addObject(NormalExcelConstants.FILE_NAME, "采购执行依据列表");
                mv.addObject(NormalExcelConstants.CLASS, CostProcurementExecutionBasisDTO.class);
                mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("采购执行依据数据", "导出人:" + userName, "采购执行依据"));
                mv.addObject(NormalExcelConstants.DATA_LIST, Arrays.asList());
                log.warn("导出异常，返回空Excel文件");
                return mv;
            } catch (Exception ex) {
                log.error("导出空Excel也失败", ex);
                throw new CdkitCloudException("导出Excel失败：" + e.getMessage());
            }
        }
    }


}

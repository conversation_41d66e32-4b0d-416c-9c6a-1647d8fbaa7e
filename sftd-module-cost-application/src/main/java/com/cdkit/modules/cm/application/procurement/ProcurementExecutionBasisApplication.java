package com.cdkit.modules.cm.application.procurement;

import com.cdkit.common.page.OrderParam;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.procurement.entity.CostProcurementExecutionBasisEntity;
import com.cdkit.modules.cm.domain.procurement.repository.CostProcurementExecutionBasisRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 采购执行依据应用服务
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProcurementExecutionBasisApplication {

    private final CostProcurementExecutionBasisRepository costProcurementExecutionBasisRepository;

    /**
     * 分页查询采购执行依据列表
     * 按照创建时间倒序排列
     * 
     * @param queryEntity 查询条件
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    public PageRes<CostProcurementExecutionBasisEntity> queryPageList(CostProcurementExecutionBasisEntity queryEntity, Integer pageNo, Integer pageSize) {
        PageReq pageReq = new PageReq();
        pageReq.setCurrent((long) pageNo);
        pageReq.setSize((long) pageSize);

        // 按照创建时间倒序排列
        OrderParam createTimeParam = new OrderParam();
        createTimeParam.setField("create_time");
        createTimeParam.setOrder("desc");
        pageReq.setOrderParam(Arrays.asList(createTimeParam));

        return costProcurementExecutionBasisRepository.queryPageList(queryEntity, pageReq);
    }

    /**
     * 根据ID查询采购执行依据详情（包含明细）
     * 
     * @param id 采购执行依据ID
     * @return 采购执行依据实体
     */
    public CostProcurementExecutionBasisEntity getById(String id) {
        return costProcurementExecutionBasisRepository.getById(id);
    }

    /**
     * 获取导出数据列表（包含明细数据）
     *
     * @param queryEntity 查询条件
     * @return 导出数据列表
     */
    public List<CostProcurementExecutionBasisEntity> getExportList(CostProcurementExecutionBasisEntity queryEntity) {
        try {
            // 直接使用仓储的findAll方法获取所有符合条件的数据（包含明细）
            List<CostProcurementExecutionBasisEntity> entityList = costProcurementExecutionBasisRepository.findAll(queryEntity);

            // 处理空结果的情况
            if (entityList == null) {
                log.info("查询结果为空，返回空列表");
                return java.util.Collections.emptyList();
            }

            log.info("获取导出数据成功，共{}条记录", entityList.size());
            return entityList;

        } catch (Exception e) {
            log.error("获取导出数据失败", e);
            // 返回空列表而不是抛出异常，这样可以导出空Excel
            log.warn("获取导出数据失败，返回空列表以支持导出空Excel");
            return java.util.Collections.emptyList();
        }
    }

    /**
     * 保存采购执行依据
     * 
     * @param entity 采购执行依据实体
     * @return 保存后的实体
     */
    public CostProcurementExecutionBasisEntity save(CostProcurementExecutionBasisEntity entity) {
        return costProcurementExecutionBasisRepository.save(entity);
    }

    /**
     * 更新采购执行依据
     * 
     * @param entity 采购执行依据实体
     * @return 更新后的实体
     */
    public CostProcurementExecutionBasisEntity update(CostProcurementExecutionBasisEntity entity) {
        return costProcurementExecutionBasisRepository.update(entity);
    }

    /**
     * 根据ID删除采购执行依据
     * 
     * @param id 采购执行依据ID
     */
    public void deleteById(String id) {
        costProcurementExecutionBasisRepository.deleteById(id);
    }

    /**
     * 批量删除采购执行依据
     *
     * @param ids 采购执行依据ID列表
     */
    public void deleteBatch(List<String> ids) {
        costProcurementExecutionBasisRepository.deleteBatch(ids);
    }

    /**
     * 根据物料编码查询采购执行依据信息（包含明细）
     * 一个物料编码可能对应多个不同的季度数据
     *
     * @param materialCode 物料编码
     * @return 采购执行依据列表（包含所有季度的数据）
     */
    public List<CostProcurementExecutionBasisEntity> findByMaterialCode(String materialCode) {
        if (materialCode == null || materialCode.trim().isEmpty()) {
            throw new IllegalArgumentException("物料编码不能为空");
        }
        return costProcurementExecutionBasisRepository.findByMaterialCode(materialCode.trim());
    }

    /**
     * 根据物料编码和季度查询采购执行依据信息（包含明细）
     *
     * @param materialCode 物料编码
     * @param quarter 所在季度
     * @return 采购执行依据实体
     */
    public CostProcurementExecutionBasisEntity findByMaterialCodeAndQuarter(String materialCode, String quarter) {
        if (materialCode == null || materialCode.trim().isEmpty()) {
            throw new IllegalArgumentException("物料编码不能为空");
        }
        if (quarter == null || quarter.trim().isEmpty()) {
            throw new IllegalArgumentException("季度不能为空");
        }
        return costProcurementExecutionBasisRepository.findByMaterialCodeAndQuarter(materialCode.trim(), quarter.trim());
    }

    /**
     * 查询所有物料编码和物料名称组合（去重）
     * 用于物料选择下拉框等场景
     *
     * @return 物料信息列表（去重后的物料编码、物料名称、计量单位）
     */
    public List<CostProcurementExecutionBasisEntity> findAllMaterials() {
        return costProcurementExecutionBasisRepository.findAllMaterials();
    }
}
